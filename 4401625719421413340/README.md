# 水资源配置优化系统 - NSGA-II多目标优化

## 项目简介

本项目是一个基于NSGA-II算法的水资源配置多目标优化系统，用于解决八个乡镇的水资源分配问题。系统考虑多种水源（地表水、引黄水、引江水、地下水、微咸水、再生水）和多种用户需求（城镇生活、农村生活、工业三产、生态、农业）。

## 主要功能

### 1. 基础优化方法
- 使用修复算法进行水资源配置
- 确保满足各种约束条件
- 生成详细的分析报告

### 2. NSGA-II多目标优化
- **目标1**: 最小化总缺水量
- **目标2**: 最大化微咸水使用量
- 生成帕累托前沿解集
- 提供多个优化方案供决策选择

### 3. 快速测试模式
- 小规模参数快速验证算法
- 适用于初步测试和调试

## 文件结构

```
4401625719421413340/
├── main.py           # 主程序文件
├── 代码数据.xlsx     # 数据文件
└── README.md         # 使用说明
```

## 数据文件说明

Excel文件包含以下工作表：
- **农业需水**: 各乡镇农业用水需求（按月）
- **固定需水**: 城镇生活、农村生活、工业三产、生态用水需求
- **地下水**: 各乡镇地下水可用量限制
- **微咸水**: 2-3g/L和3-5g/L微咸水可用量
- **地表水**: 各乡镇地表水可用量

## 使用方法

### 运行程序
```bash
python main.py
```

### 选择优化方法
程序启动后会显示菜单：
1. 基础优化方法
2. NSGA-II多目标优化  
3. NSGA-II快速测试

输入对应数字选择运行模式。

### 输出结果

#### 基础优化
- 生成 `analysis_result.xlsx` 文件，包含详细的水资源配置方案

#### NSGA-II优化
- 生成帕累托前沿图 `pareto_front.png`
- 显示多个优化解的特征分析
- 生成最优解的详细配置方案

## 算法参数

### NSGA-II参数设置
- **种群大小**: 50（完整优化）/ 20（快速测试）
- **进化代数**: 30（完整优化）/ 10（快速测试）
- **交叉概率**: 0.8
- **变异概率**: 0.2

可在代码中调整这些参数以获得不同的优化效果。

## 约束条件

系统考虑以下约束：
1. **水源供应限制**: 各类水源的月度和年度供应上限
2. **需水量平衡**: 满足各用户的基本需水需求
3. **微咸水配比**: 微咸水使用需要一定比例的淡水稀释
4. **时间约束**: 引黄水仅在特定月份可用
5. **空间约束**: 考虑乡镇间的水源调配关系

## 技术特点

### 多目标优化
- 使用NSGA-II算法处理多目标冲突
- 生成帕累托最优解集
- 为决策者提供多种平衡方案

### 智能修复机制
- 自动修复不可行解
- 确保所有约束条件得到满足
- 优化水资源配置效率

### 详细分析报告
- 按乡镇、用户、月份的详细配置
- 水源利用率分析
- 缺水量统计和分布

## 依赖库

```python
numpy
pandas
matplotlib
deap
openpyxl
```

## 注意事项

1. 确保数据文件路径正确
2. 运行前检查所有依赖库是否已安装
3. NSGA-II优化可能需要较长时间，建议先运行快速测试
4. 结果文件会保存在程序运行目录下

## 扩展功能

系统支持以下扩展：
- 调整优化目标权重
- 增加新的约束条件
- 修改算法参数
- 添加新的水源类型或用户类别

## 联系方式

如有问题或建议，请联系开发团队。
