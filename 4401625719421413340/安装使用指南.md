# 水资源配置NSGA-II优化系统 - 安装使用指南

## 1. 环境准备

### Python版本要求
- Python 3.7 或更高版本

### 安装依赖包
在项目目录下运行：
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install numpy pandas matplotlib deap openpyxl
```

## 2. 文件说明

```
4401625719421413340/
├── main.py              # 主程序（包含NSGA-II算法）
├── 代码数据.xlsx        # 水资源数据文件
├── test_nsga2.py        # 测试脚本
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
└── 安装使用指南.md      # 本文件
```

## 3. 快速开始

### 步骤1：测试环境
```bash
cd 4401625719421413340
python test_nsga2.py
```

如果测试通过，会显示"所有测试通过!"

### 步骤2：运行主程序
```bash
python main.py
```

### 步骤3：选择优化方法
程序会显示菜单：
```
水资源配置优化系统
1. 基础优化方法
2. NSGA-II多目标优化
3. NSGA-II快速测试
请选择优化方法 (1/2/3):
```

## 4. 功能详解

### 基础优化方法（选项1）
- 使用原有的修复算法
- 快速生成一个可行解
- 输出详细的Excel分析报告

### NSGA-II多目标优化（选项2）
- 运行完整的NSGA-II算法
- 种群大小：50，进化代数：30
- 生成帕累托前沿解集
- 输出多个优化方案

### NSGA-II快速测试（选项3）
- 小规模参数测试
- 种群大小：20，进化代数：10
- 快速验证算法功能

## 5. 输出结果

### Excel分析报告
- `analysis_result.xlsx`：详细的水资源配置方案
- 包含各乡镇、各用户、各月份的供水配置
- 水源利用情况和缺水量统计

### 帕累托前沿图
- `pareto_front.png`：多目标优化结果可视化
- 显示缺水量与微咸水使用量的权衡关系

## 6. 算法参数调整

在`main.py`中可以调整以下参数：

```python
# NSGA-II参数
population_size = 50    # 种群大小
generations = 30        # 进化代数
crossover_prob = 0.8    # 交叉概率
mutation_prob = 0.2     # 变异概率
```

## 7. 常见问题

### Q1: 导入deap模块失败
**解决方案**：
```bash
pip install deap
```

### Q2: Excel文件读取错误
**解决方案**：
- 确保`代码数据.xlsx`文件在正确位置
- 检查文件是否损坏
- 确保安装了openpyxl包

### Q3: 程序运行时间过长
**解决方案**：
- 先运行"NSGA-II快速测试"验证功能
- 减小种群大小和进化代数
- 使用更快的计算机

### Q4: 内存不足
**解决方案**：
- 减小种群大小
- 关闭其他程序释放内存

## 8. 技术支持

### 算法原理
- NSGA-II：非支配排序遗传算法II
- 多目标优化：同时最小化缺水量和最大化微咸水使用量
- 约束处理：智能修复机制确保解的可行性

### 性能优化建议
1. 首次运行建议使用快速测试模式
2. 根据计算机性能调整参数
3. 大规模优化建议在性能较好的机器上运行

## 9. 扩展开发

### 添加新的优化目标
在`evaluate`函数中修改目标函数定义

### 修改约束条件
在`repair_individual`函数中调整约束逻辑

### 自定义算法参数
在主程序中修改NSGA-II参数设置

## 10. 版本信息

- 版本：1.0
- 更新日期：2024年
- 主要功能：集成NSGA-II多目标优化算法
