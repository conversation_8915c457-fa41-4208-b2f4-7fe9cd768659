# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from deap import base, creator, tools, algorithms
import random
import time

# 月份数量
MONTHS = 12
TOWN_NAMES = ["王桥乡", "房寨镇", "馆陶镇", "寿山寺", "柴堡镇", "南徐村", "路桥乡", "魏僧寨镇"]

# 年份类型
YEAR_TYPES = ['平水年', '偏枯水年']

# 引江水分给八个乡镇的比例
YANGTZE_WATER_RATIOS = [0.09, 0.06, 0.32, 0.15, 0.12, 0.07, 0.09, 0.1]
YELLOW_WATER_RATIOS = [0.2, 0.17, 0.02, 0.005, 0.05, 0.165, 0.32, 0.07]
# 再生水年量数据（单位：万m³/年）
RECLAIMED_WATER_ANNUAL = {
    "王桥乡": 49.5,    # 30万m³/年 → 25000 m³/月
    "房寨镇": 8.25,     # 5万m³/年 → 4166.67 m³/月
    "馆陶镇": 165,   # 100万m³/年 → 83333.33 m³/月
    "寿山寺": 8.25,
    "柴堡镇": 8.25,
    "南徐村": 8.25,
    "路桥乡": 49.5,
    "魏僧寨镇": 33
}

# ================== 数据模型定义 ==================

class Town:
    def __init__(self, name, is_main_channel, branch_source=None):
        self.name = name
        self.is_main_channel = is_main_channel
        self.branch_source = branch_source


class WaterConfig:
    def __init__(self, excel_path, year_type):
        self.excel_path = excel_path
        self.year_type = year_type
        self.towns = self._init_towns()
        self.users = ["城镇生活", "农村生活", "工业三产", "生态", "农业"]
        self.sources = ["地表水", "引黄水", "引江水", "地下水", "微咸水2-3g/L", "微咸水3-5g/L","再生水"]
        try:
            self._load_data()
        except FileNotFoundError:
            print(f"错误：未找到文件 {excel_path}。")
            raise
        except Exception as e:
            print(f"读取文件时发生错误：{e}")
            raise

    def _init_towns(self):
        return {
            "王桥乡": Town("王桥乡", True),
            "房寨镇": Town("房寨镇", False, ["王桥乡", "馆陶镇"]),
            "馆陶镇": Town("馆陶镇", True),
            "寿山寺": Town("寿山寺", True),
            "柴堡镇": Town("柴堡镇", True),
            "南徐村": Town("南徐村", True),
            "路桥乡": Town("路桥乡", True),
            "魏僧寨镇": Town("魏僧寨镇", False, ["南徐村", "路桥乡"])
        }

    def _load_data(self):
        # 读取农业需水
        agri_demand = pd.read_excel(self.excel_path, sheet_name='农业需水', index_col=0)
        # 检查农业需水数据的月份数量
        if agri_demand.shape[1] != MONTHS:
            raise ValueError(f"农业需水数据的月份数量应为 {MONTHS} 个月，但实际为 {agri_demand.shape[1]} 个月。")
        self.agri_demand = np.array(agri_demand.loc[TOWN_NAMES].values, dtype=float) * 1e4  # 转换为m³/月

        # 读取地下水限制
        gw_limit = pd.read_excel(self.excel_path, sheet_name='地下水', index_col=0)
        self.gw_limit = np.array(gw_limit.loc[TOWN_NAMES].values, dtype=float) * 1e4  # 转换为m³/月
        
        # 读取地表水限制
        surface_limit = pd.read_excel(self.excel_path, sheet_name='地表水', index_col=0)
        self.surface_limit = np.array(surface_limit.loc[TOWN_NAMES].values, dtype=float) * 1e4  # 转换为m³/月
        # 读取微咸水数据
        mc_data = pd.read_excel(self.excel_path, sheet_name='微咸水', index_col=[0, 1])
        self.mc_limits = {
            "2-3g/L": np.array(mc_data.xs('2-3g/L', level=1).reindex(TOWN_NAMES).values, dtype=float) * 1e4,
            "3-5g/L": np.array(mc_data.xs('3-5g/L', level=1).reindex(TOWN_NAMES).values, dtype=float) * 1e4
        }
        # 读取固定需水数据
        fixed_demand = pd.read_excel(self.excel_path, sheet_name='固定需水', index_col=0)
        self.fixed_demand = (np.array(fixed_demand.loc[TOWN_NAMES].values, dtype=float) * 1e4) / 12  # 转换为m³/月

        # 工程能力
        self.engineering_cap = {
            "引黄水": 1300e4 / 3,  # 引黄水年总量 1300 万 m³ 转换为月均
            "引江水": 1300e4 / 12  # 引江水年总量 1300 万 m³ 转换为月均
        }
        # 新增再生水初始化（年量转月量）
        self.reclaimed_water_monthly = np.array([
            (RECLAIMED_WATER_ANNUAL[town] * 1e4) / 12  # 万m³转m³并均分至每月
            for town in TOWN_NAMES
        ])

    @property
    def reclaimed_water(self):
        """返回各乡镇再生水的月均供应量（m³/月）"""
        return self.reclaimed_water_monthly
    @property
    def demand(self):
        """构建三维需水矩阵 (乡镇, 用户, 月份)"""
        demand = np.zeros((len(TOWN_NAMES), len(self.users), MONTHS))
        for town_idx in range(len(TOWN_NAMES)):
            demand[town_idx, 0, :] = self.fixed_demand[town_idx, 0]  # 城镇生活
            demand[town_idx, 1, :] = self.fixed_demand[town_idx, 1]  # 农村生活
            demand[town_idx, 2, :] = self.fixed_demand[town_idx, 2]  # 工业三产
            demand[town_idx, 3, :] = self.fixed_demand[town_idx, 3]  # 生态
            demand[:, 4, :] = self.agri_demand  # 农业需水
        return demand


# 变量生成函数（乡镇×用户×月份×水源）
def gen_individual():
    """生成随机个体，增加初始多样性"""
    ind = []
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                # 生成随机的初始值而不是全零
                sources = [random.uniform(0, 10000) for _ in range(7)]
                ind.extend(sources)
    return creator.Individual(ind)


def gen_diverse_individual():
    """生成多样化的个体"""
    ind = []
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                # 根据需求量生成更合理的初始值
                demand = config.demand[town_idx, user_idx, month] if hasattr(config, 'demand') else 10000
                sources = [random.uniform(0, demand * 0.3) for _ in range(7)]
                ind.extend(sources)
    return creator.Individual(ind)


# 简化的修复函数 - 只保证基本可行性，不强制统一
def simple_repair(individual):
    """简化修复函数，保持多样性"""
    X = np.array(individual).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
    
    # 只进行基本的约束检查，不强制修复
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                for src_idx in range(len(config.sources)):
                    # 确保非负
                    X[town_idx, user_idx, month, src_idx] = max(0, X[town_idx, user_idx, month, src_idx])
                
                # 简单的供需平衡调整
                total_supply = np.sum(X[town_idx, user_idx, month, :])
                demand = config.demand[town_idx, user_idx, month]
                
                # 如果供应过多，按比例缩减
                if total_supply > demand * 1.5:  # 允许一定的超供
                    ratio = (demand * 1.2) / total_supply  # 保留20%的缓冲
                    X[town_idx, user_idx, month, :] *= ratio
    
    return creator.Individual(X.flatten())


def repair_individual(individual):
    """主修复函数"""
    return simple_repair(individual)


# 软约束评估函数
def evaluate_soft(individual):
    """软约束评估函数"""
    X = np.array(individual).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
    total_deficit = 0
    total_mc = 0
    penalty = 0
    
    # 计算基本目标
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            for user_idx in range(len(config.users)):
                demand = config.demand[town_idx, user_idx, month]
                supply = np.sum(X[town_idx, user_idx, month, :])
                deficit = max(0, demand - supply)
                total_deficit += deficit
                total_mc += np.sum(X[town_idx, user_idx, month, 4:6])  # 微咸水
    
    # 简化的约束惩罚
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            # 地表水限制惩罚
            surface_used = np.sum(X[town_idx, :, month, 0])
            surface_limit = config.surface_limit[town_idx, month]
            if surface_used > surface_limit:
                penalty += (surface_used - surface_limit) * 5
            
            # 地下水限制惩罚
            gw_used = np.sum(X[town_idx, :, month, 3])
            gw_limit = config.gw_limit[town_idx, month]
            if gw_used > gw_limit:
                penalty += (gw_used - gw_limit) * 5
    
    return (total_deficit + penalty, -total_mc)


def evaluate(individual):
    """评估函数"""
    return evaluate_soft(individual)


# 首先创建适应度和个体类
creator.create("FitnessMulti", base.Fitness, weights=(-1.0, 1.0))
creator.create("Individual", list, fitness=creator.FitnessMulti)

# 这里需要指定年份类型，可根据实际情况修改
year_type = '平水年'
config = WaterConfig("代码数据.xlsx", year_type)

# 初始化工具箱
toolbox = base.Toolbox()
toolbox.register("individual", gen_individual)
toolbox.register("population", tools.initRepeat, list, toolbox.individual)
toolbox.register("evaluate", evaluate)
toolbox.register("mate", tools.cxTwoPoint)
toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=5000, indpb=0.3)
toolbox.register("select", tools.selNSGA2)


# ================== 字体配置 ==================
def setup_chinese_font():
    """配置中文字体显示"""
    import platform
    system = platform.system()

    if system == "Darwin":  # macOS
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Heiti TC', 'SimHei']
    elif system == "Windows":  # Windows
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']

    plt.rcParams['axes.unicode_minus'] = False
    print("✓ 中文字体配置完成")


# ================== NSGA-II算法实现 ==================
def simplified_nsga2_algorithm(population_size=50, generations=30):
    """
    简化版NSGA-II算法 - 专注于保持多样性
    """
    print("开始简化版NSGA-II多目标优化...")
    print(f"种群大小: {population_size}, 进化代数: {generations}")

    # 多样化初始化
    population = []
    print("生成多样化初始种群...")

    # 50%随机个体，50%多样化个体
    for i in range(population_size // 2):
        population.append(gen_individual())
    for i in range(population_size - population_size // 2):
        population.append(gen_diverse_individual())

    # 轻度修复（保持多样性）
    print("轻度修复初始种群...")
    for i, ind in enumerate(population):
        population[i] = simple_repair(ind)

    # 评估初始种群
    print("评估初始种群...")
    fitnesses = list(map(evaluate, population))
    for ind, fit in zip(population, fitnesses):
        ind.fitness.values = fit

    # 记录进化过程
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean, axis=0)
    stats.register("min", np.min, axis=0)
    stats.register("max", np.max, axis=0)

    # 开始进化
    for gen in range(generations):
        print(f"\n第 {gen+1}/{generations} 代进化...")
        start_time = time.time()

        # 生成子代
        offspring = algorithms.varAnd(population, toolbox, 0.8, 0.3)

        # 轻度修复子代
        for i, ind in enumerate(offspring):
            offspring[i] = simple_repair(ind)

        # 评估子代
        fitnesses = list(map(evaluate, offspring))
        for ind, fit in zip(offspring, fitnesses):
            ind.fitness.values = fit

        # NSGA-II选择
        combined_pop = population + offspring
        population = tools.selNSGA2(combined_pop, population_size)

        # 统计信息
        record = stats.compile(population)

        # 检查多样性
        obj1_values = [ind.fitness.values[0] for ind in population]
        obj2_values = [ind.fitness.values[1] for ind in population]
        diversity1 = np.std(obj1_values) if len(set(obj1_values)) > 1 else 0.0
        diversity2 = np.std(obj2_values) if len(set(obj2_values)) > 1 else 0.0
        total_diversity = diversity1 + diversity2

        end_time = time.time()
        print(f"第 {gen+1} 代完成，用时: {end_time - start_time:.2f}秒")
        print(f"平均适应度: 缺水量={record['avg'][0]:.0f}, 微咸水使用量={record['avg'][1]:.0f}")
        print(f"种群多样性: {total_diversity:.2f}")
        print(f"不同解的数量: 目标1={len(set(obj1_values))}, 目标2={len(set(obj2_values))}")

        # 如果多样性过低，注入新个体
        if total_diversity < 1000 and gen > 5:
            print("🔄 注入新个体增加多样性...")
            num_new = population_size // 4
            new_individuals = [gen_diverse_individual() for _ in range(num_new)]
            for ind in new_individuals:
                ind = simple_repair(ind)
                ind.fitness.values = evaluate(ind)

            # 替换最差的个体
            population.sort(key=lambda x: x.fitness.values[0], reverse=True)
            population[-num_new:] = new_individuals

    print("\n简化版NSGA-II优化完成!")
    return population


def extract_pareto_front(population):
    """提取帕累托前沿解"""
    pareto_front = tools.sortNondominated(population, len(population), first_front_only=True)[0]
    return pareto_front


def plot_pareto_front(pareto_front, save_path=None):
    """绘制帕累托前沿图"""
    setup_chinese_font()

    plt.figure(figsize=(10, 8))

    # 提取目标函数值
    obj1 = [ind.fitness.values[0] for ind in pareto_front]  # 缺水量
    obj2 = [-ind.fitness.values[1] for ind in pareto_front]  # 微咸水使用量（转换回正值）

    plt.scatter(obj1, obj2, c='red', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
    plt.xlabel('总缺水量 (m³)', fontsize=12)
    plt.ylabel('微咸水使用量 (m³)', fontsize=12)
    plt.title('NSGA-II帕累托前沿解集', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)

    # 添加数据点标注
    for i, (x, y) in enumerate(zip(obj1, obj2)):
        plt.annotate(f'解{i+1}', (x, y), xytext=(5, 5), textcoords='offset points',
                    fontsize=8, bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

    # 添加统计信息
    plt.text(0.02, 0.98, f'解的数量: {len(pareto_front)}', transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"帕累托前沿图已保存到: {save_path}")

    plt.show()


def analyze_pareto_solutions(pareto_front):
    """分析帕累托前沿解的特征"""
    print("\n==================== 帕累托前沿解分析 ====================")
    print(f"帕累托前沿解数量: {len(pareto_front)}")

    # 提取目标函数值
    obj1_values = [ind.fitness.values[0] for ind in pareto_front]  # 缺水量
    obj2_values = [-ind.fitness.values[1] for ind in pareto_front]  # 微咸水使用量（转换为正值）

    # 检查多样性
    diversity1 = np.std(obj1_values) if len(set(obj1_values)) > 1 else 0.0
    diversity2 = np.std(obj2_values) if len(set(obj2_values)) > 1 else 0.0
    total_diversity = diversity1 + diversity2

    print(f"解的多样性指标: {total_diversity:.2f}")
    print(f"不同缺水量解的数量: {len(set(obj1_values))}")
    print(f"不同微咸水使用量解的数量: {len(set(obj2_values))}")

    if total_diversity > 1000:
        print("✅ 多样性良好！")
    else:
        print("⚠️ 多样性较低")

    print(f"\n缺水量范围: {min(obj1_values):.0f} - {max(obj1_values):.0f} m³")
    print(f"微咸水使用量范围: {min(obj2_values):.0f} - {max(obj2_values):.0f} m³")

    # 找到极端解
    min_deficit_idx = obj1_values.index(min(obj1_values))
    max_mc_idx = obj2_values.index(max(obj2_values))

    print(f"\n最小缺水量解: 缺水量={obj1_values[min_deficit_idx]:.0f}, 微咸水={obj2_values[min_deficit_idx]:.0f}")
    print(f"最大微咸水解: 缺水量={obj1_values[max_mc_idx]:.0f}, 微咸水={obj2_values[max_mc_idx]:.0f}")

    return {
        'pareto_front': pareto_front,
        'min_deficit_solution': pareto_front[min_deficit_idx],
        'max_mc_solution': pareto_front[max_mc_idx],
        'obj1_values': obj1_values,
        'obj2_values': obj2_values,
        'diversity': total_diversity
    }


# ================== 主程序 ==================
def run_basic_optimization():
    """运行基础优化方法"""
    print("运行基础水资源配置优化...")
    individual_length = len(gen_individual())
    initial_individual = creator.Individual([0.0] * individual_length)
    repaired_individual = simple_repair(initial_individual)

    print("基础优化完成，开始分析结果...")
    fitness = evaluate(repaired_individual)
    print(f"基础解适应度: 缺水量={fitness[0]:.0f}, 微咸水使用量={-fitness[1]:.0f}")

    return repaired_individual


def run_nsga2_optimization():
    """运行NSGA-II多目标优化"""
    print("运行简化版NSGA-II多目标优化...")
    print("预计运行时间: 5-15分钟，请耐心等待...")

    # 运行简化版NSGA-II算法
    final_population = simplified_nsga2_algorithm(
        population_size=40,   # 适中的种群大小
        generations=25        # 适中的进化代数
    )

    # 提取帕累托前沿
    pareto_front = extract_pareto_front(final_population)

    # 分析帕累托前沿解
    analysis_results = analyze_pareto_solutions(pareto_front)

    # 绘制帕累托前沿图
    plot_pareto_front(pareto_front, save_path="pareto_front.png")

    if analysis_results['diversity'] > 100:
        print("\n✅ 算法收敛良好，成功生成多样化解集！")
    else:
        print("\n⚠️ 算法收敛不理想，但已尽力生成解集")

    return analysis_results


if __name__ == "__main__":
    print("水资源配置优化系统")
    print("1. 基础优化方法 - 快速获得可行解")
    print("2. NSGA-II多目标优化 - 简化版帕累托前沿分析")

    # 配置中文字体
    setup_chinese_font()

    choice = input("\n请选择优化方法 (1/2): ").strip()

    if choice == "1":
        run_basic_optimization()
    elif choice == "2":
        run_nsga2_optimization()
    else:
        print("无效选择，运行基础优化方法...")
        run_basic_optimization()
