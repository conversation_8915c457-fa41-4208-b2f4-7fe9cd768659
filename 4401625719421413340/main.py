# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from deap import base, creator, tools, algorithms
import random
import time

# 月份数量
MONTHS = 12
TOWN_NAMES = ["王桥乡", "房寨镇", "馆陶镇", "寿山寺", "柴堡镇", "南徐村", "路桥乡", "魏僧寨镇"]

# 年份类型
YEAR_TYPES = ['平水年', '偏枯水年']

# 引江水分给八个乡镇的比例
YANGTZE_WATER_RATIOS = [0.09, 0.06, 0.32, 0.15, 0.12, 0.07, 0.09, 0.1]
YELLOW_WATER_RATIOS = [0.2, 0.17, 0.02, 0.005, 0.05, 0.165, 0.32, 0.07]
# 再生水年量数据（单位：万m³/年）
RECLAIMED_WATER_ANNUAL = {
    "王桥乡": 49.5,    # 30万m³/年 → 25000 m³/月
    "房寨镇": 8.25,     # 5万m³/年 → 4166.67 m³/月
    "馆陶镇": 165,   # 100万m³/年 → 83333.33 m³/月
    "寿山寺": 8.25,
    "柴堡镇": 8.25,
    "南徐村": 8.25,
    "路桥乡": 49.5,
    "魏僧寨镇": 33
}

# ================== 数据模型定义 ==================

class Town:
    def __init__(self, name, is_main_channel, branch_source=None):
        self.name = name
        self.is_main_channel = is_main_channel
        self.branch_source = branch_source


class WaterConfig:
    def __init__(self, excel_path, year_type):
        self.excel_path = excel_path
        self.year_type = year_type
        self.towns = self._init_towns()
        self.users = ["城镇生活", "农村生活", "工业三产", "生态", "农业"]
        self.sources = ["地表水", "引黄水", "引江水", "地下水", "微咸水2-3g/L", "微咸水3-5g/L","再生水"]
        try:
            self._load_data()
        except FileNotFoundError:
            print(f"错误：未找到文件 {excel_path}。")
        except Exception as e:
            print(f"读取文件时发生错误：{e}")

    def _init_towns(self):
        return {
            "王桥乡": Town("王桥乡", True),
            "房寨镇": Town("房寨镇", False, ["王桥乡", "馆陶镇"]),
            "馆陶镇": Town("馆陶镇", True),
            "寿山寺": Town("寿山寺", True),
            "柴堡镇": Town("柴堡镇", True),
            "南徐村": Town("南徐村", True),
            "路桥乡": Town("路桥乡", True),
            "魏僧寨镇": Town("魏僧寨镇", False, ["南徐村", "路桥乡"])
        }

    def _load_data(self):
        # 读取农业需水
        agri_demand = pd.read_excel(self.excel_path, sheet_name='农业需水', index_col=0)
        # 检查农业需水数据的月份数量
        if agri_demand.shape[1] != MONTHS:
            raise ValueError(f"农业需水数据的月份数量应为 {MONTHS} 个月，但实际为 {agri_demand.shape[1]} 个月。")
        self.agri_demand = agri_demand.loc[TOWN_NAMES].values * 1e4  # 转换为m³/月

        # 读取地下水限制
        gw_limit = pd.read_excel(self.excel_path, sheet_name='地下水', index_col=0)
        self.gw_limit = gw_limit.loc[TOWN_NAMES].values * 1e4  # 转换为m³/月
        # print("读取到的地下水数据")
        # print(self.gw_limit)
        # 读取地表水限制
        surface_limit = pd.read_excel(self.excel_path, sheet_name='地表水', index_col=0)
        self.surface_limit = surface_limit.loc[TOWN_NAMES].values * 1e4  # 转换为m³/月
        # 读取微咸水数据
        mc_data = pd.read_excel(self.excel_path, sheet_name='微咸水', index_col=[0, 1])
        self.mc_limits = {
            "2-3g/L": mc_data.xs('2-3g/L', level=1).reindex(TOWN_NAMES).values * 1e4,
            "3-5g/L": mc_data.xs('3-5g/L', level=1).reindex(TOWN_NAMES).values * 1e4
        }
        # 读取固定需水数据
        fixed_demand = pd.read_excel(self.excel_path, sheet_name='固定需水', index_col=0)
        self.fixed_demand = (fixed_demand.loc[TOWN_NAMES].values * 1e4) / 12  # 转换为m³/月

        # 工程能力
        self.engineering_cap = {
            "引黄水": 1300e4 / 3,  # 引黄水年总量 1300 万 m³ 转换为月均
            "引江水": 1300e4 / 12  # 引江水年总量 1300 万 m³ 转换为月均
        }
        # 新增再生水初始化（年量转月量）
        self.reclaimed_water_monthly = np.array([
            (RECLAIMED_WATER_ANNUAL[town] * 1e4) / 12  # 万m³转m³并均分至每月
            for town in TOWN_NAMES
        ])

    @property
    def reclaimed_water(self):
        """返回各乡镇再生水的月均供应量（m³/月）"""
        return self.reclaimed_water_monthly
    @property
    def demand(self):
        """构建三维需水矩阵 (乡镇, 用户, 月份)"""
        demand = np.zeros((len(TOWN_NAMES), len(self.users), MONTHS))
        for town_idx in range(len(TOWN_NAMES)):
            demand[town_idx, 0, :] = self.fixed_demand[town_idx, 0]  # 城镇生活
            demand[town_idx, 1, :] = self.fixed_demand[town_idx, 1]  # 农村生活
            demand[town_idx, 2, :] = self.fixed_demand[town_idx, 2]  # 工业三产
            demand[town_idx, 3, :] = self.fixed_demand[town_idx, 3]  # 生态
            demand[:, 4, :] = self.agri_demand  # 农业需水
        return demand


# 变量生成函数（乡镇×用户×月份×水源）
# 变量生成函数（乡镇×用户×月份×水源）
def gen_individual():
    ind = []
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                sources = [0] * 7
                ind.extend(sources)
    return creator.Individual(ind)


# 修复函数确保变量不越界
def repair_individual(individual):
    repaired = [0.0] * len(individual)
    # 初始化每个乡镇的引江水和引黄水全年配额
    yangtze_annual_available = {
        town_idx: config.engineering_cap["引江水"] * 12 * YANGTZE_WATER_RATIOS[town_idx]
        for town_idx in range(len(TOWN_NAMES))
    }
    yellow_annual_available = {
        town_idx: config.engineering_cap["引黄水"] * 3 * YELLOW_WATER_RATIOS[town_idx]
        for town_idx in range(len(TOWN_NAMES))
    }
    # 初始化每个乡镇每月的引江水和引黄水可用量
    yangtze_monthly_available = {
        (town_idx, month): config.engineering_cap["引江水"] * YANGTZE_WATER_RATIOS[town_idx]
        for town_idx in range(len(TOWN_NAMES))
        for month in range(MONTHS)
    }
    yellow_monthly_available = {
        (town_idx, month): config.engineering_cap["引黄水"] * YELLOW_WATER_RATIOS[town_idx] if month in [1, 2, 3] else 0
        for town_idx in range(len(TOWN_NAMES))
        for month in range(MONTHS)
    }

    # 用于记录每个乡镇每个月引黄水的剩余量
    yellow_remaining = {
        (town_idx, month): yellow_monthly_available[(town_idx, month)]
        for town_idx in range(len(TOWN_NAMES))
        for month in range(MONTHS)
    }

    # 用于记录每个乡镇每个月地下水的剩余量
    gw_remaining = {
        (town_idx, month): config.gw_limit[town_idx, month]
        for town_idx in range(len(TOWN_NAMES))
        for month in range(MONTHS)
    }
    surface_remaining = {
        (town_idx, month): config.surface_limit[town_idx, month]
        for town_idx in range(len(TOWN_NAMES))
        for month in range(MONTHS)
    }

    # 复制需求矩阵，用于后续更新剩余需求
    remaining_demand = config.demand.copy()

    agri_freshwater_used = {
        (town_idx, month, src_idx): 0.0
        for town_idx in range(8)
        for month in range(12)
        for src_idx in [0, 1, 3]  # 地表水(0)、引黄水(1)、地下水(3)
    }
    # 微咸水分配
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                # 初始化剩余微咸水变量
                remaining_mc2_3 = 0
                remaining_mc3_5 = 0
                if user_idx == 4:
                    # if user_idx == 4 and month in [1, 2, 3, 6]:
                    max_mc2_3 = config.mc_limits["2-3g/L"][town_idx, month]
                    max_mc3_5 = config.mc_limits["3-5g/L"][town_idx, month]
                    remaining_mc2_3 = max_mc2_3
                    remaining_mc3_5 = max_mc3_5
                    demand = remaining_demand[town_idx, user_idx, month]
                    while demand > 0 and 0 < max_mc2_3:
                        alloc2_3 = min(demand, max_mc2_3)
                        repaired[idx + 4] += alloc2_3
                        max_mc2_3 -= alloc2_3
                        demand -= alloc2_3
                        remaining_demand[town_idx, user_idx, month] -= alloc2_3
                        remaining_mc2_3 -= alloc2_3
                    while demand > 0 and 0 < max_mc3_5:
                        alloc3_5 = min(demand, max_mc3_5)
                        repaired[idx + 5] += alloc3_5
                        max_mc3_5 -= alloc3_5
                        demand -= alloc3_5
                        remaining_demand[town_idx, user_idx, month] -= alloc3_5
                        remaining_mc3_5 -= alloc3_5

                # **新增：分配剩余微咸水给工业用户（user_idx=2）**
                if remaining_mc2_3 > 0 or remaining_mc3_5 > 0:
                    industrial_idx = (town_idx * len(config.users) * MONTHS + 2 * MONTHS + month) * len(config.sources)
                    industrial_demand = remaining_demand[town_idx, 2, month]

                    # 分配微咸水2-3g/L
                    if remaining_mc2_3 > 0 and industrial_demand > 0:
                        alloc = min(remaining_mc2_3, industrial_demand)
                        repaired[industrial_idx + 4] = alloc
                        remaining_demand[town_idx, 2, month] -= alloc
                        remaining_mc2_3 -= alloc

                    # 分配微咸水3-5g/L
                    if remaining_mc3_5 > 0 and industrial_demand > 0:
                        alloc = min(remaining_mc3_5, industrial_demand)
                        repaired[industrial_idx + 5] = alloc
                        remaining_demand[town_idx, 2, month] -= alloc
                        remaining_mc3_5 -= alloc
        # ================== 新增再生水分配逻辑 ==================
        # 再生水分配（优先生态用户，其次工业用户）
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            # 1. 分配给生态用户（user_idx=3）
            user_idx = 3
            idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
            demand = remaining_demand[town_idx, user_idx, month]
            available = config.reclaimed_water[town_idx]  # 使用月均量
            remaining_zai=available

            if available > 0 and demand > 0:
                alloc = min(available, demand)
                repaired[idx + 6] = alloc  # 再生水索引6
                remaining_demand[town_idx, user_idx, month] -= alloc
                remaining_zai -= alloc



            # 2. 分配给工业用户（user_idx=2）
            user_idx = 2
            idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
            demand = remaining_demand[town_idx, user_idx, month]
            # 计算生态用户分配后剩余的再生水量
            remaining_reclaimed = remaining_zai
            if remaining_reclaimed > 0 and demand > 0:
                alloc = min(remaining_reclaimed, demand)
                repaired[idx + 6] += alloc
                remaining_demand[town_idx, user_idx, month] -= alloc
    #    # 引江水分配
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            if user_idx not in [3,4]:
                for month in range(MONTHS):
                    #if month not in [ 5, 6,7]:
                        idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                        demand = remaining_demand[town_idx, user_idx, month]
                        available = min(yangtze_monthly_available[(town_idx, month)],
                                        yangtze_annual_available[town_idx])
                        if available > 0 and demand > 0:
                            alloc = min(available, demand)
                            repaired[idx + 2] = alloc
                            yangtze_monthly_available[(town_idx, month)] -= alloc
                            yangtze_annual_available[town_idx] -= alloc
                            # yangze_remaining[(town_idx, month)] -= alloc
                            remaining_demand[town_idx, user_idx, month] -= alloc
    # 引黄水分配
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            if user_idx == 4:
                for month in [1, 2, 3]:
                    idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                    demand = remaining_demand[town_idx, user_idx, month]
                    available = yellow_monthly_available[(town_idx, month)]
                    if available > 0 and demand > 0:
                        alloc = min(available, demand)
                        repaired[idx + 1] = alloc
                        agri_freshwater_used[(town_idx, month, 1)] += alloc  # 记录农业使用的引黄水
                        yellow_monthly_available[(town_idx, month)] -= alloc
                        yellow_annual_available[town_idx] -= alloc
                        yellow_remaining[(town_idx, month)] -= alloc
                        remaining_demand[town_idx, user_idx, month] -= alloc

    # 地表水分配
    # 地表水分配
    # 第一步：先分配当月的地表水
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                demand = remaining_demand[town_idx, user_idx, month]
                surface_max = config.surface_limit[town_idx, month]  # 当前月份的地表水限制
                if month in [8, 9, 10, 11] and user_idx in[0,1,2,4]:
                    gw_max = config.gw_limit[town_idx, month]
                    while gw_max > 0 and demand > 0:
                        alloc = min(gw_max, demand)
                        repaired[idx + 3] += alloc
                        gw_max -= alloc
                        demand -= alloc
                        agri_freshwater_used[(town_idx, month, 3)] += alloc  # 记录农业使用的地表水
                        remaining_demand[town_idx, user_idx, month] = demand
                        gw_remaining[(town_idx, month)] -= alloc
                        config.gw_limit[town_idx, month] = gw_max  # 更新地xia水剩余量

                while surface_max > 0 and demand > 0:
                    alloc = min(surface_max, demand)
                    repaired[idx + 0] += alloc
                    surface_max -= alloc
                    demand -= alloc
                    agri_freshwater_used[(town_idx, month, 0)] += alloc
                    remaining_demand[town_idx, user_idx, month] -= alloc
                    surface_remaining[(town_idx, month)] = surface_max
                    config.surface_limit[town_idx, month] = surface_max

    # 第二步：统一处理上个月剩余地表水的分配
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(1, MONTHS):  # 1月暂不考虑12月的剩余量
            prev_month = month - 1
            prev_surface_remaining = surface_remaining[(town_idx, prev_month)]
            if prev_surface_remaining > 0:
                # 遍历所有用户，尝试分配上个月的剩余地表水
                for user_idx in range(len(config.users)):
                    idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                    demand = remaining_demand[town_idx, user_idx, month]
                    while prev_surface_remaining > 0 and demand > 0:
                        alloc = min(prev_surface_remaining, demand)
                        repaired[idx + 0] += alloc
                        prev_surface_remaining -= alloc
                        demand -= alloc
                        agri_freshwater_used[(town_idx, month, 0)] += alloc
                        remaining_demand[town_idx, user_idx, month] -= alloc
                        if prev_surface_remaining > 0:
                            surface_remaining[(town_idx, month)] += prev_surface_remaining
                            config.surface_limit[town_idx, month] += prev_surface_remaining
                            surface_remaining[(town_idx, prev_month)] = 0
                        else:

                            surface_remaining[(town_idx, prev_month)] = prev_surface_remaining

                # 如果上个月的剩余地表水还有剩余，加到本月的剩余地表水中
                # if prev_surface_remaining > 0:
                #    surface_remaining[(town_idx, month)] += prev_surface_remaining
                #    config.surface_limit[town_idx, month] += prev_surface_remaining
        # 处理1月份考虑12月份剩余水量
    last_month = MONTHS - 1  # 12月的索引
    for town_idx in range(len(TOWN_NAMES)):
        dec_remaining = surface_remaining[(town_idx, last_month)]
        if dec_remaining > 0:
            for user_idx in range(len(config.users)):
                idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + 0) * len(config.sources)
                demand = remaining_demand[town_idx, user_idx, 0]
                while dec_remaining > 0 and demand > 0:
                    alloc = min(dec_remaining, demand)
                    repaired[idx + 0] += alloc
                    dec_remaining -= alloc
                    demand -= alloc
                    agri_freshwater_used[(town_idx, month, 0)] += alloc
                    remaining_demand[town_idx, user_idx, 0] -= alloc
                    if dec_remaining > 0:
                        surface_remaining[(town_idx, 0)] += dec_remaining
                        config.surface_limit[town_idx, 0] += dec_remaining
                        surface_remaining[(town_idx, month)] = 0
                    else:
                        surface_remaining[(town_idx, last_month)] = dec_remaining



    # 地下水分配
    # 第一步：先分配当月的地下水
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in [0,1,2,4]:
            for month in range(MONTHS):
                idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                demand = remaining_demand[town_idx, user_idx, month]
                gw_max = config.gw_limit[town_idx, month]
                while gw_max > 0 and demand > 0:
                    alloc = min(gw_max, demand)
                    repaired[idx + 3] += alloc
                    gw_max -= alloc
                    demand -= alloc
                    agri_freshwater_used[(town_idx, month, 3)] += alloc  # 记录农业使用的地下水
                    remaining_demand[town_idx, user_idx, month] = demand
                    gw_remaining[(town_idx, month)] -= alloc
                    config.gw_limit[town_idx, month] = gw_max  # 更新地下水剩余量
    # 第二步：统一处理上个月剩余地下水的分配
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(1, MONTHS):
            prev_month = month - 1
            prev_gw_remaining = gw_remaining[(town_idx, prev_month)]
            if prev_gw_remaining > 0:
                # 遍历所有用户，尝试分配上个月的剩余地下水
                gw_remaining[(town_idx, month)] += prev_gw_remaining
                gw_remaining[(town_idx, prev_month)] -= prev_gw_remaining
                for user_idx in [0,1,2,4]:
                    idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                    demand = remaining_demand[town_idx, user_idx, month]
                    if demand > 0:
                        alloc = min(gw_remaining[(town_idx, month)], demand)
                        repaired[idx + 3] += alloc
                        # prev_gw_remaining -= alloc
                        demand -= alloc
                        agri_freshwater_used[(town_idx, month, 3)] += alloc  # 记录农业使用的地下水
                        remaining_demand[town_idx, user_idx, month] -= alloc
                        gw_remaining[(town_idx, month)] -= alloc

    # 第三步 将11月给1月 2月等用
    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            prev_month = (month - 1) % MONTHS
            prev_gw_remaining = gw_remaining[(town_idx, prev_month)]
            if prev_gw_remaining > 0:
                # 遍历所有用户，尝试分配上个月的剩余地下水
                gw_remaining[(town_idx, month)] += prev_gw_remaining
                gw_remaining[(town_idx, prev_month)] -= prev_gw_remaining
                for user_idx in [0,1,2,4]:
                    idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                    demand = remaining_demand[town_idx, user_idx, month]
                    if demand > 0:
                        alloc = min(gw_remaining[(town_idx, month)], demand)
                        repaired[idx + 3] += alloc
                        # prev_gw_remaining -= alloc
                        demand -= alloc
                        agri_freshwater_used[(town_idx, month, 3)] += alloc  # 记录农业使用的地下水
                        remaining_demand[town_idx, user_idx, month] -= alloc
                        gw_remaining[(town_idx, month)] -= alloc

    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            user_idx = 4  # 农业用户
            idx = (town_idx * 5 * 12 + user_idx * 12 + month) * 6
            mc2_3 = repaired[idx + 4]
            mc3_5 = repaired[idx + 5]

            # 计算农业用户已使用的淡水总量（地表水+引黄水+地下水）
            total_agri_freshwater = sum([
                agri_freshwater_used[(town_idx, month, src_idx)]
                for src_idx in [0, 1, 3]  # 地表水、引黄水、地下水
            ])

            # 计算所需淡水
            required_fresh = calculate_freshwater_needed(town_idx, month, mc2_3, mc3_5)
            if total_agri_freshwater < required_fresh:
                if check_freshwater_availability(total_agri_freshwater, town_idx, month, max_mc2_3, 0):
                    alloc_2_3 = max_mc2_3
                    remaining_fresh = total_agri_freshwater - calculate_freshwater_needed(town_idx,
                                                                                                 month,
                                                                                                 max_mc2_3, 0)
                    # 计算可分配的3 - 5g/L
                    if month in [3, 6]:
                        max_3_5 = remaining_fresh * 4
                    else:
                        max_3_5 = remaining_fresh * 1.5
                    alloc_3_5 = min(max_3_5, mc3_5)
                    excess_mc3_5 = mc3_5 - alloc_3_5
                    repaired[idx + 5]=excess_mc3_5
                    remaining_demand[town_idx, user_idx, month] += excess_mc3_5





    # 总量平衡
    for town_idx in range(len(TOWN_NAMES)):
        for user_idx in range(len(config.users)):
            for month in range(MONTHS):
                idx = (town_idx * len(config.users) * MONTHS + user_idx * MONTHS + month) * len(config.sources)
                source_values = repaired[idx:idx + len(config.sources)]
                total_supply = sum(source_values)
                demand = config.demand[town_idx, user_idx, month]
                if total_supply > demand:
                    ratio = demand / total_supply
                    source_values = [v * ratio for v in source_values]
                    repaired[idx:idx + len(config.sources)] = source_values

    return creator.Individual(repaired)


# 计算微咸水所需淡水量
def calculate_freshwater_needed(town_idx, month, mc2_3, mc3_5):
    year_type = config.year_type
    freshwater_needed = 0
    if month in [1, 2, 3, 6]:
        if year_type == '平水年':
            if month in [3, 6]:
                freshwater_needed += mc3_5 * 0.25  # 平水年3/6月为1/4
            else:
                freshwater_needed += mc3_5 * (2 / 3)
        elif year_type == '偏枯水年':
            freshwater_needed += mc3_5 * (2 / 3)
    return freshwater_needed


# 检查淡水资源是否足够配置微咸水
def check_freshwater_availability(agricultural_freshwater_used, town_idx, month, mc2_3, mc3_5):
    # 淡水总量 = (地表水 + 引黄水 + 地下水) / 3
    surface = config.surface_limit[town_idx, month]
    yellow = config.engineering_cap["引黄水"] * YELLOW_WATER_RATIOS[town_idx] if month in [1, 2, 3] else 0
    groundwater = config.gw_limit[town_idx, month]
    total_fresh = (surface + yellow + groundwater) / 3
    freshwater_needed = calculate_freshwater_needed(town_idx, month, mc2_3, mc3_5)

    return total_fresh >= freshwater_needed


def evaluate(individual):
    """
    评估函数 - 计算两个目标函数值
    目标1: 最小化总缺水量
    目标2: 最大化微咸水使用量（转换为负值进行最小化）
    """
    repaired_ind = repair_individual(individual)
    X = np.array(repaired_ind).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
    total_deficit = 0
    total_mc = 0

    for town_idx in range(len(TOWN_NAMES)):
        for month in range(MONTHS):
            # 计算总缺水量和微咸水使用量
            for user_idx in range(len(config.users)):
                demand = config.demand[town_idx, user_idx, month]
                supply = np.sum(X[town_idx, user_idx, month, :])
                deficit = max(0, demand - supply)
                total_deficit += deficit
                # 微咸水使用量（索引4和5分别是2-3g/L和3-5g/L微咸水）
                total_mc += np.sum(X[town_idx, user_idx, month, 4:6])

    return (total_deficit, -total_mc)  # 负号使微咸水使用量最大化


def custom_mutate(individual, mu=0, sigma=1000, indpb=0.1):
    """
    自定义变异函数，考虑水资源配置的特点
    """
    mutated_ind = individual[:]

    for i in range(len(mutated_ind)):
        if random.random() < indpb:
            # 添加高斯噪声
            mutated_ind[i] += random.gauss(mu, sigma)
            # 确保非负
            mutated_ind[i] = max(0, mutated_ind[i])

    return (creator.Individual(mutated_ind),)


def custom_crossover(ind1, ind2):
    """
    自定义交叉函数，适用于水资源配置问题
    """
    # 使用均匀交叉
    size = min(len(ind1), len(ind2))
    for i in range(size):
        if random.random() < 0.5:
            ind1[i], ind2[i] = ind2[i], ind1[i]

    return ind1, ind2


# 这里需要指定年份类型，可根据实际情况修改
year_type = '平水年'
config = WaterConfig("4401625719421413340/代码数据.xlsx", year_type)
creator.create("FitnessMulti", base.Fitness, weights=(-1.0, 1.0))
creator.create("Individual", list, fitness=creator.FitnessMulti)
# 初始化工具箱
toolbox = base.Toolbox()
toolbox.register("individual", gen_individual)
toolbox.register("population", tools.initRepeat, list, toolbox.individual)
toolbox.register("evaluate", evaluate)
toolbox.register("mate", custom_crossover)
toolbox.register("mutate", custom_mutate, mu=0, sigma=1000, indpb=0.1)
toolbox.register("select", tools.selNSGA2)


# ================== 结果分析 ==================
# ================== 结果分析 ==================
def analyze_solution(individual):
    X = np.array(individual).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
    import os
    home_dir = os.path.expanduser("~")
    file_path = os.path.join(home_dir, 'analysis_result.xlsx')

    try:
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # ------------------------- 各用户每月分水源供水量 -------------------------
            monthly_source_data = []
            for town_idx, town_name in enumerate(TOWN_NAMES):
                for user_idx, user in enumerate(config.users):
                    for month in range(MONTHS):
                        for src_idx, src in enumerate(config.sources):
                            supply = X[town_idx, user_idx, month, src_idx]
                            monthly_source_data.append([
                                town_name,
                                user,
                                month + 1,  # 月份从1开始
                                src,
                                int(round(supply))  # 强制转换为整数
                            ])
            df_monthly_source = pd.DataFrame(
                monthly_source_data,
                columns=["乡镇名称", "用户类型", "月份", "水源类型", "供水量(m³)"]
            )
            df_monthly_source.to_excel(writer, sheet_name='各用户每月分水源供水', index=False)

            # ------------------------- 各城镇分行业情况 -------------------------
            data_list = []
            for town_idx, town_name in enumerate(TOWN_NAMES):
                for user_idx, user in enumerate(config.users):
                    total_demand = np.sum(config.demand[town_idx, user_idx, :])
                    surface_water_supply = np.sum(X[town_idx, user_idx, :, 0])
                    yellow_river_water_supply = np.sum(X[town_idx, user_idx, :, 1])
                    yangtze_river_water_supply = np.sum(X[town_idx, user_idx, :, 2])
                    groundwater_supply = np.sum(X[town_idx, user_idx, :, 3])
                    mc2_3_supply = np.sum(X[town_idx, user_idx, :, 4])
                    mc3_5_supply = np.sum(X[town_idx, user_idx, :, 5])
                    reclaimed_supply = np.sum(X[town_idx, user_idx, :, 6])  # 新增再生水
                    total_supply = surface_water_supply + yellow_river_water_supply + yangtze_river_water_supply + groundwater_supply + mc2_3_supply + mc3_5_supply + reclaimed_supply
                    deficit = max(0, total_demand - total_supply)
                    # 对结果进行取整操作
                    data_list.append([
                        town_name, user,
                        round(total_demand),
                        round(surface_water_supply),
                        round(yellow_river_water_supply),
                        round(yangtze_river_water_supply),
                        round(groundwater_supply),
                        round(mc2_3_supply),
                        round(mc3_5_supply),
                        round(reclaimed_supply),
                        round(total_supply),
                        round(deficit)
                    ])
            df = pd.DataFrame(data_list,
                              columns=["乡镇名称", "用户类型", "总需水量", "地表水供水量", "引黄水供水量",
                                       "引江水供水量", "地下水供水量", "微咸水2 - 3g/L供水量",
                                       "微咸水3 - 5g/L供水量", "再生水供水量","总供水量", "缺水量"])
            df.to_excel(writer, sheet_name='各城镇分行业情况', index=False)

            # 乡镇分类别需水供水情况
            data_list = []
            for town_idx, town_name in enumerate(TOWN_NAMES):
                for user_idx, user in enumerate(config.users):
                    total_demand = np.sum(config.demand[town_idx, user_idx, :])
                    total_supply = np.sum(X[town_idx, user_idx, :, :])
                    deficit = max(0, total_demand - total_supply)
                    total_demand = round(total_demand)
                    total_supply = round(total_supply)
                    deficit = round(deficit)
                    data_list.append([town_name, user, total_demand, total_supply, deficit])
            df = pd.DataFrame(data_list, columns=["乡镇名称", "用户类型", "总需水量", "总供水量", "缺水量"])
            df.to_excel(writer, sheet_name='乡镇分类别情况', index=False)
            # 各乡镇每月情况
            for town_idx, town_name in enumerate(TOWN_NAMES):
                monthly_data_list = []
                source_usage_list = []
                for month in range(MONTHS):
                    for user_idx, user in enumerate(config.users):
                        demand = config.demand[town_idx, user_idx, month]
                        supply = np.sum(X[town_idx, user_idx, month, :])
                        deficit = max(0, demand - supply)
                        demand = round(demand)
                        supply = round(supply)
                        deficit = round(deficit)
                        monthly_data_list.append([month + 1, user, demand, supply, deficit])
                    for src_idx, src in enumerate(config.sources):
                        usage = np.sum(X[town_idx, :, month, src_idx])
                        usage = round(usage)
                        source_usage_list.append([month + 1, src, usage])
                df_monthly = pd.DataFrame(monthly_data_list,
                                          columns=["月份", "用户类型", "总需求", "总供水量", "缺水量"])
                df_source_usage = pd.DataFrame(source_usage_list, columns=["月份", "水源", "使用量"])
                df_monthly.to_excel(writer, sheet_name=f'{town_name}每月情况', index=False)
                df_source_usage.to_excel(writer, sheet_name=f'{town_name}每月水源利用', index=False)

            # 各乡镇全年供用水及缺水情况汇总
            data_list = []
            for town_idx, town_name in enumerate(TOWN_NAMES):
                annual_demand = np.sum(config.demand[town_idx, :, :])
                annual_supply = np.sum(X[town_idx, :, :, :])
                annual_deficit = max(0, annual_demand - annual_supply)
                annual_demand = round(annual_demand)
                annual_supply = round(annual_supply)
                annual_deficit = round(annual_deficit)
                data_list.append([town_name, annual_demand, annual_supply, annual_deficit])
            df = pd.DataFrame(data_list, columns=["乡镇名称", "总需水量", "总供水量", "总缺水量"])
            df.to_excel(writer, sheet_name='各乡镇全年汇总', index=False)

        print("分析结果已保存到", file_path, "文件中。")
    except PermissionError:
        print("没有权限写入文件，请检查文件路径或关闭正在使用该文件的程序。")


# ================== NSGA-II算法实现 ==================
def nsga2_algorithm(population_size=100, generations=50, crossover_prob=0.8, mutation_prob=0.2):
    """
    NSGA-II多目标优化算法

    参数:
    - population_size: 种群大小
    - generations: 进化代数
    - crossover_prob: 交叉概率
    - mutation_prob: 变异概率

    返回:
    - 最终的帕累托前沿解集
    """
    print("开始NSGA-II多目标优化...")
    print(f"种群大小: {population_size}, 进化代数: {generations}")

    # 初始化种群
    population = toolbox.population(n=population_size)

    # 修复初始种群
    print("修复初始种群...")
    for i, ind in enumerate(population):
        population[i] = repair_individual(ind)
        if i % 20 == 0:
            print(f"已修复 {i+1}/{population_size} 个个体")

    # 评估初始种群
    print("评估初始种群...")
    fitnesses = list(map(toolbox.evaluate, population))
    for ind, fit in zip(population, fitnesses):
        ind.fitness.values = fit

    # 记录进化过程
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean, axis=0)
    stats.register("min", np.min, axis=0)
    stats.register("max", np.max, axis=0)

    logbook = tools.Logbook()
    logbook.header = "gen", "avg", "min", "max"

    # 开始进化
    for gen in range(generations):
        print(f"\n第 {gen+1}/{generations} 代进化...")
        start_time = time.time()

        # 选择父代
        offspring = algorithms.varAnd(population, toolbox, crossover_prob, mutation_prob)

        # 修复子代
        for i, ind in enumerate(offspring):
            offspring[i] = repair_individual(ind)

        # 评估子代
        fitnesses = list(map(toolbox.evaluate, offspring))
        for ind, fit in zip(offspring, fitnesses):
            ind.fitness.values = fit

        # 合并父代和子代
        combined_pop = population + offspring

        # NSGA-II选择
        population = toolbox.select(combined_pop, population_size)

        # 记录统计信息
        record = stats.compile(population)
        logbook.record(gen=gen, **record)

        end_time = time.time()
        print(f"第 {gen+1} 代完成，用时: {end_time - start_time:.2f}秒")
        print(f"平均适应度: 缺水量={record['avg'][0]:.0f}, 微咸水使用量={record['avg'][1]:.0f}")
        print(f"最小缺水量: {record['min'][0]:.0f}")
        print(f"最大微咸水使用量: {record['max'][1]:.0f}")

    print("\nNSGA-II优化完成!")
    return population, logbook


def extract_pareto_front(population):
    """
    提取帕累托前沿解
    """
    pareto_front = tools.sortNondominated(population, len(population), first_front_only=True)[0]
    return pareto_front


def plot_pareto_front(pareto_front, save_path=None):
    """
    绘制帕累托前沿图
    """
    plt.figure(figsize=(10, 8))

    # 提取目标函数值
    obj1 = [ind.fitness.values[0] for ind in pareto_front]  # 缺水量
    obj2 = [ind.fitness.values[1] for ind in pareto_front]  # 微咸水使用量

    plt.scatter(obj1, obj2, c='red', s=50, alpha=0.7)
    plt.xlabel('总缺水量 (m³)', fontsize=12)
    plt.ylabel('微咸水使用量 (m³)', fontsize=12)
    plt.title('NSGA-II帕累托前沿', fontsize=14)
    plt.grid(True, alpha=0.3)

    # 添加数据点标注
    for i, (x, y) in enumerate(zip(obj1, obj2)):
        plt.annotate(f'{i+1}', (x, y), xytext=(5, 5), textcoords='offset points', fontsize=8)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"帕累托前沿图已保存到: {save_path}")

    plt.show()


def analyze_pareto_solutions(pareto_front):
    """
    分析帕累托前沿解的特征
    """
    print("\n==================== 帕累托前沿解分析 ====================")
    print(f"帕累托前沿解数量: {len(pareto_front)}")

    # 提取目标函数值
    obj1_values = [ind.fitness.values[0] for ind in pareto_front]  # 缺水量
    obj2_values = [ind.fitness.values[1] for ind in pareto_front]  # 微咸水使用量

    print(f"\n缺水量范围: {min(obj1_values):.0f} - {max(obj1_values):.0f} m³")
    print(f"微咸水使用量范围: {min(obj2_values):.0f} - {max(obj2_values):.0f} m³")

    # 找到极端解
    min_deficit_idx = obj1_values.index(min(obj1_values))
    max_mc_idx = obj2_values.index(max(obj2_values))

    print(f"\n最小缺水量解: 缺水量={obj1_values[min_deficit_idx]:.0f}, 微咸水={obj2_values[min_deficit_idx]:.0f}")
    print(f"最大微咸水解: 缺水量={obj1_values[max_mc_idx]:.0f}, 微咸水={obj2_values[max_mc_idx]:.0f}")

    return {
        'pareto_front': pareto_front,
        'min_deficit_solution': pareto_front[min_deficit_idx],
        'max_mc_solution': pareto_front[max_mc_idx],
        'obj1_values': obj1_values,
        'obj2_values': obj2_values
    }


# ================== 主程序 ==================
def run_basic_optimization():
    """运行基础优化（原有方法）"""
    print("运行基础水资源配置优化...")
    individual_length = len(gen_individual())
    initial_individual = creator.Individual([0.0] * individual_length)
    repaired_individual = repair_individual(initial_individual)

    print("基础优化完成，开始分析结果...")
    analyze_solution(repaired_individual)

    return repaired_individual


def run_nsga2_optimization():
    """运行NSGA-II多目标优化"""
    print("运行NSGA-II多目标优化...")

    # 运行NSGA-II算法
    final_population, _ = nsga2_algorithm(
        population_size=50,  # 可根据需要调整
        generations=30,      # 可根据需要调整
        crossover_prob=0.8,
        mutation_prob=0.2
    )

    # 提取帕累托前沿
    pareto_front = extract_pareto_front(final_population)

    # 分析帕累托前沿解
    analysis_results = analyze_pareto_solutions(pareto_front)

    # 绘制帕累托前沿图
    plot_pareto_front(pareto_front, save_path="pareto_front.png")

    # 分析最优解
    print("\n分析最小缺水量解...")
    analyze_solution(analysis_results['min_deficit_solution'])

    return analysis_results


def run_quick_test():
    """快速测试NSGA-II算法（小规模参数）"""
    print("运行NSGA-II快速测试...")

    # 运行小规模NSGA-II算法
    final_population, _ = nsga2_algorithm(
        population_size=20,   # 小种群
        generations=10,       # 少代数
        crossover_prob=0.8,
        mutation_prob=0.2
    )

    # 提取帕累托前沿
    pareto_front = extract_pareto_front(final_population)

    # 简单分析
    print(f"\n快速测试完成！帕累托前沿解数量: {len(pareto_front)}")

    if pareto_front:
        best_solution = pareto_front[0]
        print(f"示例解的适应度: 缺水量={best_solution.fitness.values[0]:.0f}, 微咸水使用量={-best_solution.fitness.values[1]:.0f}")

        # 分析一个解
        print("\n分析示例解...")
        analyze_solution(best_solution)

    return pareto_front


if __name__ == "__main__":
    print("水资源配置优化系统")
    print("1. 基础优化方法")
    print("2. NSGA-II多目标优化")
    print("3. NSGA-II快速测试")

    choice = input("请选择优化方法 (1/2/3): ").strip()

    if choice == "1":
        run_basic_optimization()
    elif choice == "2":
        run_nsga2_optimization()
    elif choice == "3":
        run_quick_test()
    else:
        print("无效选择，运行基础优化方法...")
        run_basic_optimization()


