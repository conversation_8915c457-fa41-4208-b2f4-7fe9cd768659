#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文字体显示测试脚本
"""

import matplotlib.pyplot as plt
import numpy as np
import platform

def setup_chinese_font():
    """配置中文字体"""
    system = platform.system()
    print(f"检测到操作系统: {system}")
    
    if system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'SimHei']
    elif system == "Windows":  # Windows
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']
    
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    print(f"设置字体: {fonts}")

def test_chinese_display():
    """测试中文显示"""
    setup_chinese_font()
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    plt.figure(figsize=(10, 6))
    plt.plot(x, y1, label='缺水量', linewidth=2)
    plt.plot(x, y2, label='微咸水使用量', linewidth=2)
    
    plt.xlabel('时间 (月份)', fontsize=12)
    plt.ylabel('数值 (万m³)', fontsize=12)
    plt.title('中文字体显示测试 - 水资源配置', fontsize=14, fontweight='bold')
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    
    # 添加中文注释
    plt.text(5, 0.5, '这是中文测试文本\n包含：数字123、符号-+', 
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
             fontsize=10, ha='center')
    
    plt.tight_layout()
    plt.savefig('font_test.png', dpi=300, bbox_inches='tight', facecolor='white')
    print("✓ 字体测试图片已保存为 font_test.png")
    plt.show()

def check_available_fonts():
    """检查可用字体"""
    from matplotlib import font_manager
    
    print("\n=== 系统可用中文字体 ===")
    fonts = font_manager.findSystemFonts()
    chinese_fonts = []
    
    for font_path in fonts:
        try:
            font_prop = font_manager.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            # 检查是否包含中文字体关键词
            if any(keyword in font_name.lower() for keyword in 
                   ['simhei', 'simsun', 'microsoft', 'yahei', 'heiti', 'arial unicode', 'wenquanyi']):
                chinese_fonts.append(font_name)
        except:
            continue
    
    if chinese_fonts:
        print("找到以下中文字体:")
        for font in set(chinese_fonts)[:10]:  # 显示前10个
            print(f"  - {font}")
    else:
        print("未找到明确的中文字体，将使用系统默认字体")

def main():
    """主函数"""
    print("=== 中文字体显示测试 ===")
    
    # 检查可用字体
    check_available_fonts()
    
    # 测试中文显示
    print("\n=== 开始字体显示测试 ===")
    try:
        test_chinese_display()
        print("✓ 字体测试完成！")
        print("如果图片中的中文显示正常，说明字体配置成功")
        print("如果显示为方块，请安装中文字体包或更新matplotlib")
    except Exception as e:
        print(f"✗ 字体测试失败: {e}")
        print("请检查matplotlib是否正确安装")

if __name__ == "__main__":
    main()
