#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版NSGA-II算法测试脚本
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 先导入必要的库
    from deap import base, creator, tools, algorithms
    print("✓ DEAP库导入成功")
    
    # 创建适应度和个体类
    if not hasattr(creator, 'FitnessMulti'):
        creator.create("FitnessMulti", base.Fitness, weights=(-1.0, 1.0))
    if not hasattr(creator, 'Individual'):
        creator.create("Individual", list, fitness=creator.FitnessMulti)
    print("✓ 创建适应度和个体类成功")
    
    # 导入主模块
    from main_fixed import *
    print("✓ 成功导入简化版主模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 初始化失败: {e}")
    sys.exit(1)

def test_individual_generation():
    """测试个体生成"""
    print("\n=== 测试个体生成 ===")
    
    try:
        # 测试随机个体生成
        ind1 = gen_individual()
        print(f"✓ 随机个体生成成功，长度: {len(ind1)}")
        
        # 测试多样化个体生成
        ind2 = gen_diverse_individual()
        print(f"✓ 多样化个体生成成功，长度: {len(ind2)}")
        
        # 检查两个个体是否不同
        if ind1 != ind2:
            print("✓ 生成的个体具有多样性")
        else:
            print("⚠️ 生成的个体相同，可能存在问题")
        
        return True
    except Exception as e:
        print(f"✗ 个体生成测试失败: {e}")
        return False

def test_repair_function():
    """测试修复函数"""
    print("\n=== 测试修复函数 ===")
    
    try:
        # 生成测试个体
        individual = gen_individual()
        print("✓ 生成测试个体成功")
        
        # 测试简化修复
        repaired = simple_repair(individual)
        print("✓ 简化修复成功")
        
        # 检查修复后的个体
        X = np.array(repaired).reshape(len(TOWN_NAMES), len(config.users), MONTHS, len(config.sources))
        
        # 检查是否有负值
        has_negative = np.any(X < 0)
        if not has_negative:
            print("✓ 修复后无负值")
        else:
            print("⚠️ 修复后仍有负值")
        
        # 检查供需比例
        total_supply = np.sum(X)
        total_demand = np.sum(config.demand)
        ratio = total_supply / total_demand
        print(f"✓ 供需比例: {ratio:.2f}")
        
        return True
    except Exception as e:
        print(f"✗ 修复函数测试失败: {e}")
        return False

def test_evaluation_function():
    """测试评估函数"""
    print("\n=== 测试评估函数 ===")
    
    try:
        # 生成并修复个体
        individual = gen_individual()
        repaired = simple_repair(individual)
        
        # 测试评估
        fitness = evaluate(repaired)
        print(f"✓ 评估成功: 缺水量={fitness[0]:.0f}, 微咸水使用量={-fitness[1]:.0f}")
        
        # 测试多个个体的评估差异
        fitness_list = []
        for i in range(5):
            ind = gen_diverse_individual()
            rep = simple_repair(ind)
            fit = evaluate(rep)
            fitness_list.append(fit)
        
        # 检查适应度多样性
        obj1_values = [f[0] for f in fitness_list]
        obj2_values = [f[1] for f in fitness_list]
        
        diversity1 = len(set([round(v, -3) for v in obj1_values]))  # 四舍五入到千位
        diversity2 = len(set([round(v, -3) for v in obj2_values]))
        
        print(f"✓ 适应度多样性: 目标1有{diversity1}种不同值, 目标2有{diversity2}种不同值")
        
        if diversity1 > 1 or diversity2 > 1:
            print("✅ 评估函数能产生多样化的适应度值")
        else:
            print("⚠️ 评估函数产生的适应度值缺乏多样性")
        
        return True
    except Exception as e:
        print(f"✗ 评估函数测试失败: {e}")
        return False

def test_simplified_nsga2():
    """测试简化版NSGA-II算法"""
    print("\n=== 测试简化版NSGA-II算法 ===")
    
    try:
        # 运行小规模测试
        final_population = simplified_nsga2_algorithm(
            population_size=10,
            generations=3
        )
        
        print(f"✓ 简化版NSGA-II算法运行成功")
        print(f"✓ 最终种群大小: {len(final_population)}")
        
        # 提取帕累托前沿
        pareto_front = extract_pareto_front(final_population)
        print(f"✓ 帕累托前沿解数量: {len(pareto_front)}")
        
        # 检查多样性
        if pareto_front:
            obj1_values = [ind.fitness.values[0] for ind in pareto_front]
            obj2_values = [ind.fitness.values[1] for ind in pareto_front]
            
            unique_obj1 = len(set([round(v, -3) for v in obj1_values]))
            unique_obj2 = len(set([round(v, -3) for v in obj2_values]))
            
            print(f"✓ 帕累托前沿多样性: 目标1有{unique_obj1}种值, 目标2有{unique_obj2}种值")
            
            if unique_obj1 > 1 or unique_obj2 > 1:
                print("✅ 成功生成多样化的帕累托前沿！")
                return True
            else:
                print("⚠️ 帕累托前沿缺乏多样性，但算法运行正常")
                return True
        else:
            print("⚠️ 未生成帕累托前沿解")
            return False
            
    except Exception as e:
        print(f"✗ 简化版NSGA-II测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始简化版NSGA-II算法测试...")
    
    # 检查数据文件
    if not os.path.exists("代码数据.xlsx"):
        print("✗ 数据文件不存在")
        return False
    else:
        print("✓ 数据文件存在")
    
    # 运行各项测试
    tests = [
        test_individual_generation,
        test_repair_function,
        test_evaluation_function,
        test_simplified_nsga2
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果: {passed}/{len(tests)} 项测试通过 ===")
    
    if passed == len(tests):
        print("✅ 所有测试通过！简化版NSGA-II算法已准备就绪")
        print("可以运行 'python main_fixed.py' 来使用优化系统")
        return True
    else:
        print("⚠️ 部分测试未通过，但基本功能可用")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n请检查错误信息")
        sys.exit(1)
